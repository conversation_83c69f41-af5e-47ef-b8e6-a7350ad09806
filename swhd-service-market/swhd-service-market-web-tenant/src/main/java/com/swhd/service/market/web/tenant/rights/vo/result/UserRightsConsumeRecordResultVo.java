package com.swhd.service.market.web.tenant.rights.vo.result;

import com.swhd.service.market.api.rights.dto.result.UserRightsConsumeRecordResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/11/15 14:46
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserRightsConsumeRecordResult对象")
public class UserRightsConsumeRecordResultVo extends UserRightsConsumeRecordResult {

    @Schema(description = "门店名称")
    private String shopName;

    @Schema(description = "消耗内容名称")
    private String bizContentName;

    @Schema(description = "操作人")
    private String operator = "系统";

    @Schema(description = "消耗业务模块")
    private String bizName;

    @Schema(description = "消耗项目")
    private String bizItemName;

}
