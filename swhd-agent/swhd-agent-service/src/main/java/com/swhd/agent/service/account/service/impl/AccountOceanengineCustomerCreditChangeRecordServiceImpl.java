package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordPageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerCreditChangeRecord;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerCreditChangeRecordMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerCreditChangeRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 自助充值额度变更记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerCreditChangeRecordServiceImpl extends BaseHdServiceImpl<AccountOceanengineCustomerCreditChangeRecordMapper, AccountOceanengineCustomerCreditChangeRecord> implements AccountOceanengineCustomerCreditChangeRecordService {

    @Override
    public IPage<AccountOceanengineCustomerCreditChangeRecord> page(AccountOceanengineCustomerCreditChangeRecordPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getSelfRechargeCreditId()), AccountOceanengineCustomerCreditChangeRecord::getSelfRechargeCreditId, param.getSelfRechargeCreditId())
                .in(Func.isNotEmpty(param.getChangeTypes()), AccountOceanengineCustomerCreditChangeRecord::getChangeType, param.getChangeTypes())
                .eq(Func.isNotEmpty(param.getRechargeDetailId()), AccountOceanengineCustomerCreditChangeRecord::getRechargeDetailId, param.getRechargeDetailId())
                .eq(Func.isNotEmpty(param.getCreatorId()), AccountOceanengineCustomerCreditChangeRecord::getCreatorId, param.getCreatorId())
                .betweenDateTimeList(param.getCreateTimeBetween(), AccountOceanengineCustomerCreditChangeRecord::getCreateTime)
                .orderByDesc(AccountOceanengineCustomerCreditChangeRecord::getCreateTime)
                .orderByDesc(AccountOceanengineCustomerCreditChangeRecord::getId)
                .page(convertToPage(param));
    }

}
