package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.api.account.client.AccountOceanengineCustomerCreditChangeRecordClient;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordPageParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerCreditChangeRecordResult;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerCreditChangeRecordResultVo;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.client.TenantUserTenantClient;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.collection.CollectionUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 自助充值额度变更记录表 Web控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCustomerCreditChangeRecord")
public class AccountOceanengineCustomerCreditChangeRecordController {

    private final AccountOceanengineCustomerCreditChangeRecordClient accountOceanengineCustomerCreditChangeRecordClient;
    private final TenantUserTenantClient tenantUserTenantClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineCustomerCreditChangeRecordResultVo>> page(@RequestBody @Valid AccountOceanengineCustomerCreditChangeRecordPageParam param) {
        // 调用原始的分页查询
        Rsp<PageResult<AccountOceanengineCustomerCreditChangeRecordResult>> rsp = accountOceanengineCustomerCreditChangeRecordClient.page(param);
        if (RspHd.isFail(rsp) || rsp.getData() == null) {
            return RspHd.fail(rsp);
        }

        PageResult<AccountOceanengineCustomerCreditChangeRecordResult> pageResult = rsp.getData();
        List<AccountOceanengineCustomerCreditChangeRecordResult> records = pageResult.getRecords();

        if (CollectionUtil.isEmpty(records)) {
            PageResult<AccountOceanengineCustomerCreditChangeRecordResultVo> voPageResult = new PageResult<>();
            voPageResult.setTotal(pageResult.getTotal());
            voPageResult.setSize(pageResult.getSize());
            voPageResult.setCurrent(pageResult.getCurrent());
            voPageResult.setRecords(List.of());
            return RspHd.data(voPageResult);
        }

        // 收集所有的创建人ID
        Set<Long> creatorIds = records.stream()
                .map(AccountOceanengineCustomerCreditChangeRecordResult::getCreatorId)
                .filter(creatorId -> creatorId != null && !creatorId.equals(""))
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        // 批量查询用户信息
        Map<Long, TenantUserTenantResult> userMap = Map.of();
        if (CollectionUtil.isNotEmpty(creatorIds)) {
            try {
                Rsp<List<TenantUserTenantResult>> userRsp = tenantUserTenantClient.listByUserIds(creatorIds);
                if (RspHd.isSuccess(userRsp) && userRsp.getData() != null) {
                    userMap = userRsp.getData().stream()
                            .collect(Collectors.toMap(TenantUserTenantResult::getUserId, user -> user));
                }
            } catch (Exception e) {
                log.warn("查询用户信息失败", e);
            }
        }

        // 转换为VO并设置操作人信息
        List<AccountOceanengineCustomerCreditChangeRecordResultVo> voRecords = records.stream()
                .map(record -> {
                    AccountOceanengineCustomerCreditChangeRecordResultVo vo = Func.copy(record, AccountOceanengineCustomerCreditChangeRecordResultVo.class);
                    if (record.getCreatorId() != null && !record.getCreatorId().equals("")) {
                        try {
                            Long creatorId = Long.valueOf(record.getCreatorId());
                            vo.setCreator(userMap.get(creatorId));
                        } catch (NumberFormatException e) {
                            log.warn("创建人ID格式错误: {}", record.getCreatorId());
                        }
                    }
                    return vo;
                })
                .collect(Collectors.toList());

        // 构建返回结果
        PageResult<AccountOceanengineCustomerCreditChangeRecordResultVo> voPageResult = new PageResult<>();
        voPageResult.setTotal(pageResult.getTotal());
        voPageResult.setSize(pageResult.getSize());
        voPageResult.setCurrent(pageResult.getCurrent());
        voPageResult.setRecords(voRecords);

        return RspHd.data(voPageResult);
    }
}
